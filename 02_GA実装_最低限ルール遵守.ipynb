{"cells": [{"cell_type": "markdown", "id": "c20d8255", "metadata": {}, "source": ["これからやること  \n", "初期在庫更新式の反映  \n", "会社データの使用  "]}, {"cell_type": "code", "execution_count": null, "id": "1f98fc65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GA生産スケジューリング最適化システム ===\n", "gen\tnevals\tavg        \tmin      \tmax      \n", "0  \t150   \t1.47221e+08\t1.184e+08\t1.731e+08\n", "1  \t107   \t1.61594e+08\t1.184e+08\t7.40262e+08\n", "2  \t120   \t1.5846e+08 \t1.1755e+08\t9.13945e+08\n", "3  \t108   \t1.4881e+08 \t1.1795e+08\t4.62821e+08\n", "4  \t106   \t1.44372e+08\t1.125e+08 \t6.19338e+08\n", "5  \t113   \t1.34734e+08\t1.0695e+08\t5.9859e+08 \n", "6  \t98    \t1.34431e+08\t1.0695e+08\t5.98621e+08\n", "7  \t108   \t1.35263e+08\t1.0485e+08\t7.22759e+08\n", "8  \t117   \t1.33271e+08\t8.97e+07  \t4.257e+08  \n", "9  \t117   \t1.2214e+08 \t9.515e+07 \t4.38331e+08\n", "10 \t105   \t1.15371e+08\t9.08e+07  \t4.69522e+08\n", "11 \t112   \t1.09641e+08\t8.25e+07  \t2.65491e+08\n", "12 \t107   \t1.05621e+08\t8e+07     \t4.13111e+08\n", "13 \t103   \t1.00566e+08\t7.335e+07 \t4.51622e+08\n", "14 \t110   \t9.09595e+07\t7.175e+07 \t1.40065e+08\n", "15 \t112   \t8.66922e+07\t6.28e+07  \t1.59353e+08\n", "16 \t105   \t8.20468e+07\t6.245e+07 \t2.01728e+08\n", "17 \t108   \t7.68539e+07\t6.245e+07 \t1.27065e+08\n", "18 \t110   \t7.26765e+07\t5.93e+07  \t1.8092e+08 \n", "19 \t111   \t7.16145e+07\t5.92e+07  \t3.74517e+08\n", "20 \t101   \t6.66768e+07\t4.99e+07  \t1.5384e+08 \n", "21 \t115   \t6.35037e+07\t4.99e+07  \t1.36584e+08\n", "22 \t91    \t6.54915e+07\t4.99e+07  \t4.07729e+08\n", "23 \t100   \t6.02478e+07\t4.09e+07  \t1.73126e+08\n", "24 \t124   \t5.57968e+07\t4.015e+07 \t1.58374e+08\n", "25 \t109   \t5.68658e+07\t3.925e+07 \t1.83712e+08\n", "26 \t102   \t5.18164e+07\t3.84e+07  \t1.33408e+08\n", "27 \t105   \t4.92117e+07\t3.775e+07 \t1.66653e+08\n", "28 \t109   \t4.45398e+07\t3.32e+07  \t1.05771e+08\n", "29 \t102   \t4.50385e+07\t3.3e+07   \t2.55257e+08\n", "30 \t98    \t4.29039e+07\t2.805e+07 \t2.1294e+08 \n", "31 \t112   \t4.00745e+07\t2.69e+07  \t1.44878e+08\n", "32 \t106   \t3.77264e+07\t2.69e+07  \t1.53178e+08\n", "33 \t111   \t3.60771e+07\t2.235e+07 \t1.64716e+08\n", "34 \t106   \t3.25746e+07\t2.235e+07 \t1.40621e+08\n", "35 \t109   \t3.04158e+07\t2e+07     \t1.379e+08  \n", "36 \t105   \t2.71632e+07\t1.95e+07  \t1.03671e+08\n", "37 \t98    \t2.43016e+07\t1.715e+07 \t7.0012e+07 \n", "38 \t103   \t2.28343e+07\t1.57e+07  \t6.89208e+07\n", "39 \t102   \t2.13096e+07\t1.105e+07 \t1.23602e+08\n", "40 \t107   \t2.14293e+07\t1.105e+07 \t2.5064e+08 \n", "41 \t111   \t1.98358e+07\t1.17e+07  \t1.42908e+08\n", "42 \t100   \t1.85862e+07\t1.105e+07 \t1.86174e+08\n", "43 \t115   \t1.61344e+07\t1.045e+07 \t1.05828e+08\n", "44 \t106   \t1.54573e+07\t8.95e+06  \t1.39613e+08\n", "45 \t109   \t1.59997e+07\t7.05e+06  \t1.82283e+08\n", "46 \t108   \t1.38182e+07\t2.65e+06  \t9.44555e+07\n", "47 \t109   \t1.34337e+07\t4.65e+06  \t7.99188e+07\n", "48 \t116   \t1.3593e+07 \t4.65e+06  \t1.25079e+08\n", "49 \t102   \t1.1507e+07 \t3.4e+06   \t7.28394e+07\n", "50 \t105   \t1.02605e+07\t3.4e+06   \t7.3903e+07 \n", "51 \t108   \t9.78622e+06\t2.65e+06  \t1.04674e+08\n", "52 \t115   \t8.00073e+06\t2.4e+06   \t8.53275e+07\n", "53 \t99    \t9.32601e+06\t1.75e+06  \t1.46834e+08\n", "54 \t109   \t9.99904e+06\t1.9e+06   \t2.13187e+08\n", "55 \t107   \t5.09304e+06\t1.9e+06   \t4.78455e+07\n", "56 \t110   \t5.43753e+06\t1.9e+06   \t1.41338e+08\n", "57 \t121   \t7.24619e+06\t1.9e+06   \t1.43083e+08\n", "58 \t106   \t6.32188e+06\t1.9e+06   \t8.91652e+07\n", "59 \t109   \t3.02765e+06\t1.6e+06   \t5.99981e+07\n", "60 \t109   \t3.46846e+06\t450000    \t4.06333e+07\n", "61 \t99    \t4.96116e+06\t450000    \t8.88822e+07\n", "62 \t110   \t4.25119e+06\t450000    \t7.99151e+07\n", "63 \t104   \t7.61014e+06\t50000     \t1.22675e+08\n", "64 \t109   \t6.44586e+06\t50000     \t9.92454e+07\n", "65 \t112   \t5.79028e+06\t50000     \t1.14183e+08\n", "66 \t92    \t3.6116e+06 \t50000     \t7.56449e+07\n", "67 \t129   \t1.82709e+06\t0         \t9.46e+07   \n", "68 \t101   \t4.46595e+06\t0         \t1.50967e+08\n", "69 \t99    \t2.01568e+06\t0         \t8.62266e+07\n", "70 \t118   \t4.13059e+06\t0         \t8.14606e+07\n", "71 \t114   \t3.27153e+06\t0         \t1.0978e+08 \n", "72 \t111   \t2.40035e+06\t0         \t1.33656e+08\n", "73 \t102   \t564147     \t0         \t2.81773e+07\n", "74 \t107   \t2.28812e+06\t0         \t1.18881e+08\n", "75 \t109   \t5.26581e+06\t0         \t2.27632e+08\n", "76 \t96    \t2.79099e+06\t0         \t8.12105e+07\n", "77 \t106   \t2.79433e+06\t0         \t1.23192e+08\n", "78 \t109   \t2.77231e+06\t0         \t7.47687e+07\n", "79 \t113   \t2.41741e+06\t0         \t1.29351e+08\n", "80 \t114   \t4.6157e+06 \t0         \t1.45306e+08\n", "81 \t104   \t1.70619e+06\t0         \t4.56e+07   \n", "82 \t116   \t4.36946e+06\t0         \t1.25654e+08\n", "83 \t99    \t3.34466e+06\t0         \t1.22675e+08\n", "84 \t100   \t4.61655e+06\t0         \t1.73222e+08\n", "85 \t97    \t3.7055e+06 \t0         \t1.20343e+08\n", "86 \t94    \t2.48681e+06\t0         \t1.26167e+08\n", "87 \t115   \t5.20475e+06\t0         \t1.62503e+08\n", "88 \t112   \t6.85777e+06\t0         \t1.7916e+08 \n", "89 \t103   \t2.45786e+06\t0         \t8.88119e+07\n", "90 \t101   \t2.35811e+06\t0         \t7.56119e+07\n", "91 \t98    \t5.00173e+06\t0         \t1.38581e+08\n", "92 \t104   \t4.64143e+06\t0         \t1.82145e+08\n", "93 \t114   \t4.517e+06  \t0         \t1.50514e+08\n", "94 \t112   \t4.54497e+06\t0         \t1.20801e+08\n", "95 \t106   \t1.8968e+06 \t0         \t5.24407e+07\n", "96 \t104   \t1.19355e+06\t0         \t8.4242e+07 \n", "97 \t107   \t2.6471e+06 \t0         \t6.86288e+07\n", "98 \t104   \t3.11859e+06\t0         \t1.53438e+08\n", "99 \t107   \t4.27315e+06\t0         \t1.52228e+08\n", "100\t102   \t4.84856e+06\t0         \t2.90793e+08\n", "\n", "=== 最適化結果 ===\n", "最良個体のペナルティ: 0.00\n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABjUAAASmCAYAAABm7inNAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdeVxU9f7H8ffACO6isiSLS2G5t2CLmVl6XUkql9RruVu5ZMnVtLpuaRcr05+W<PERSON><PERSON><PERSON>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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n", "段替え制約違反: 0 期間\n", "出荷遅れ制約違反: 0 期間\n"]}], "source": ["\"\"\"\n", "ベースコード\n", "\"\"\"\n", "\n", "import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "from deap import base, creator, tools, algorithms\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "品番数 = 0\n", "期間 = 20\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        初期在庫量リスト = []\n", "        \n", "        for row in reader:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            サイクルタイムリスト.append(float(row[header.index(\"cycle_time\")])/60)\n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "            初期在庫量リスト.append(int(row[header.index(\"initial_inventory\")]))\n", "        \n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(ind):\n", "    \"\"\"評価関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    total_setup_penalty = 0\n", "    total_overtime_penalty = 0\n", "    total_shortage_penalty = 0\n", "    \n", "    inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup = 0\n", "        \n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = ind[idx]\n", "            \n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "            \n", "            if production > 0:\n", "                setup_time = 30\n", "                daily_setup += 1\n", "            else:\n", "                setup_time = 0\n", "            \n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "                daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            \n", "            # 出荷遅れペナルティ\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                delay_penalty = 50000 * shortage_amount\n", "                total_shortage_penalty += delay_penalty\n", "                inventory[i] = 0\n", "        \n", "        # 稼働時間超過ペナルティ\n", "        if daily_time > max_daily_time:\n", "            overtime = daily_time - max_daily_time\n", "            total_overtime_penalty += 1000000 * overtime\n", "        \n", "        # 段替え制約ペナルティ\n", "        if daily_setup > 5:\n", "            total_setup_penalty += 10000 * (daily_setup - 5)\n", "    \n", "    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty\n", "    return total_penalty,\n", "\n", "def generate_ind():\n", "    \"\"\"個体生成関数\"\"\"\n", "    productions = []\n", "    temp_inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_productions = [0] * 品番数\n", "        \n", "        # 優先度を計算（在庫不足の度合いで優先順位決定）\n", "        priorities = []\n", "        for i in range(品番数):\n", "            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            risk = shortage * 出荷数リスト[i]  # 不足量 × 需要量\n", "            priorities.append((risk, i))\n", "        \n", "        priorities.sort(reverse=True)\n", "        for risk, i in priorities:\n", "            shortage = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            \n", "            if shortage > 0:\n", "                setup_time = 30\n", "                remaining_time = max_daily_time - daily_time - setup_time\n", "                \n", "                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]\n", "                    max_producible = int(remaining_time / cycle_time_per_unit)\n", "                    \n", "                    if max_producible > 0:\n", "                        # 不足分と時間制約の範囲内で生産量を決定\n", "                        target_production = min(shortage * 10, max_producible)  # 不足分の最大10倍まで\n", "                        production = random.randint(shortage, max(shortage, target_production))\n", "                        production_time = setup_time + production * cycle_time_per_unit\n", "                        \n", "                        # 稼働時間制約厳守\n", "                        if daily_time + production_time <= max_daily_time:\n", "                            daily_productions[i] = production\n", "                            daily_time += production_time\n", "        \n", "        for i in range(品番数):\n", "            productions.append(daily_productions[i])\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            temp_inventory[i] = max(0, temp_inventory[i])\n", "    \n", "    return creator.Individual(productions)\n", "\n", "def mutate(ind):\n", "    \"\"\"突然変異関数（時間制約対応版）\"\"\"\n", "    max_daily_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        if random.random() < 0.1:  # 10%の確率で期間全体を変更\n", "            for i in range(品番数):\n", "                if random.random() < 0.3:\n", "                    idx = t * 品番数 + i\n", "                    \n", "                    # 現在の生産量を基準に変更\n", "                    current_production = ind[idx]\n", "                    change = random.randint(-100, 100)\n", "                    new_production = max(0, current_production + change)\n", "                    new_production = min(2000, new_production)  # 上限制限\n", "                    \n", "                    ind[idx] = new_production\n", "    \n", "    return ind,\n", "\n", "def plot_results(best_individual):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = 初期在庫量リスト[:]\n", "    max_daily_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            idx = t * 品番数 + i\n", "            production = best_individual[idx]\n", "\n", "            # 在庫が十分にある場合は生産しない\n", "            if inventory[i] >= 出荷数リスト[i]:\n", "                production = 0\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            # 生産時間の計算\n", "            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:\n", "                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])\n", "                daily_production_time += production_time + setup_time\n", "\n", "            # 在庫更新と出荷遅れ計算\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            # 出荷遅れの計算\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    # プロット作成（2x2レイアウト）\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    axes[0, 0].set_ylim(0, 3000)\n", "\n", "    # 2. 各期間の総生産時間（制限ラインを追加）\n", "    bars_time = axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,\n", "                       label=f'上限 ({max_daily_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].set_ylim(0, 1500)\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_ylim(0, 6)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    bars_delay = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    axes[1, 1].set_ylim(0, 1000)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # 統計情報を出力\n", "    delay_violations = sum(1 for x in total_shipment_delay_per_period if x > 0)\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_time)\n", "    setup_violations = sum(1 for x in total_setup_times_per_period if x > 5)\n", "\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "    print(f\"段替え制約違反: {setup_violations} 期間\")\n", "    print(f\"出荷遅れ制約違反: {delay_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間\n", "\n", "    # CSVファイルの読み込み\n", "    result = read_csv('data_ga.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "\n", "    # DEAP設定\n", "    if hasattr(creator, '<PERSON><PERSON><PERSON>'):\n", "        del creator.FitnessMin\n", "    if hasattr(creator, 'Individual'):\n", "        del creator.Individual\n", "\n", "    creator.create(\"FitnessMin\", base.Fitness, weights=(-1.0,))\n", "    creator.create(\"Individual\", list, fitness=creator.FitnessMin)\n", "\n", "    toolbox = base.Toolbox()\n", "    toolbox.register(\"individual\", generate_ind)\n", "    toolbox.register(\"population\", tools.initRepeat, list, toolbox.individual)\n", "    toolbox.register(\"evaluate\", evaluate)\n", "    toolbox.register(\"mate\", tools.cxTwoPoint)\n", "    toolbox.register(\"mutate\", mutate)\n", "    toolbox.register(\"select\", tools.selTournament, tournsize=3)\n", "\n", "    # GAパラメータ（遅れ削減重視）\n", "    population_size = 150  # 集団サイズを増加\n", "    generations = 100      # 世代数を増加\n", "    cxpb = 0.6\n", "    mutpb = 0.3           # 突然変異率を増加\n", "\n", "    # 初期集団生成\n", "    population = toolbox.population(n=population_size)\n", "    hof = tools.HallOfFame(1)\n", "    stats = tools.Statistics(lambda ind: ind.fitness.values)\n", "    stats.register(\"avg\", np.mean)\n", "    stats.register(\"min\", np.min)\n", "    stats.register(\"max\", np.max)\n", "\n", "    # GA実行\n", "    population, logbook = algorithms.eaSimple(\n", "        population, toolbox,\n", "        cxpb=cxpb, mutpb=mutpb, ngen=generations,\n", "        stats=stats, halloffame=hof, verbose=True\n", "    )\n", "\n", "    # 結果の表示\n", "    best_ind = hof[0]\n", "    best_fitness = best_ind.fitness.values[0]\n", "\n", "    print(f\"\\n=== 最適化結果 ===\")\n", "    print(f\"最良個体のペナルティ: {best_fitness:.2f}\")\n", "\n", "    # 結果をプロット\n", "    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results(best_ind)\n", "\n", "    return best_ind, logbook\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"=== GA生産スケジューリング最適化システム ===\")\n", "    best_solution, log = main()"]}, {"cell_type": "code", "execution_count": null, "id": "19b50103", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}